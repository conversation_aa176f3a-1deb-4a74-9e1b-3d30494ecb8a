package com.howbuy.cgi.trade.simu.interceptor;

import com.alibaba.fastjson.JSON;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.util.OpsSysMonitor;
import com.howbuy.trace.RequestChainTrace;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统异常监控拦截器
 * 用于监控和告警非业务异常，如空指针、数组越界等系统级异常
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-21
 */
@Service("systemExceptionMonitorInterceptor")
public class SystemExceptionMonitorInterceptor implements HandlerInterceptor {
    
    private static final Logger LOG = LoggerFactory.getLogger(SystemExceptionMonitorInterceptor.class);
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 预处理阶段不做任何操作，让请求正常流转
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 只有当存在异常时才进行处理
        if (ex != null) {
            // 判断是否为非业务异常
            if (isSystemException(ex)) {
                // 发送系统异常告警
                sendSystemExceptionAlert(request, ex);
            }
        }
    }
    
    /**
     * 判断是否为系统异常（非业务异常）
     * 
     * @param ex 异常对象
     * @return true-系统异常，false-业务异常
     */
    private boolean isSystemException(Exception ex) {
        // 如果是BizException或其子类，则认为是业务异常
        if (ex instanceof BizException) {
            return false;
        }
        
        // 检查异常链中是否包含BizException
        Throwable cause = ex.getCause();
        while (cause != null) {
            if (cause instanceof BizException) {
                return false;
            }
            cause = cause.getCause();
        }
        return true;
    }
    
    /**
     * 发送系统异常告警
     * 
     * @param request 请求对象
     * @param ex 异常对象
     */
    private void sendSystemExceptionAlert(HttpServletRequest request, Exception ex) {
        try {
            // 构建告警信息
            Map<String, Object> alertInfo = buildAlertInfo(request, ex);
            String alertMessage = JSON.toJSONString(alertInfo);
            
            // 使用OpsSysMonitor发送告警
            OpsSysMonitor.warn(alertMessage, OpsSysMonitor.ERROR);
            
            LOG.error("系统异常告警已发送: {}", alertMessage, ex);
            
        } catch (Exception alertEx) {
            // 告警发送失败时记录日志，但不影响主流程
            LOG.error("发送系统异常告警失败", alertEx);
        }
    }
    
    /**
     * 构建告警信息
     * 
     * @param request 请求对象
     * @param ex 异常对象
     * @return 告警信息Map
     */
    private Map<String, Object> buildAlertInfo(HttpServletRequest request, Exception ex) {
        Map<String, Object> alertInfo = new HashMap<>();
        
        // 基本信息
        alertInfo.put("alertType", "SYSTEM_EXCEPTION");
        alertInfo.put("requestUri", request.getRequestURI());
        alertInfo.put("requestMethod", request.getMethod());
        alertInfo.put("traceId", RequestChainTrace.getReqId());
        alertInfo.put("remoteAddr", getClientIpAddress(request));
        
        // 异常信息
        alertInfo.put("exceptionType", ex.getClass().getSimpleName());
        alertInfo.put("exceptionMessage", ex.getMessage());
        
        // 获取异常堆栈的前几行，避免日志过长
        StackTraceElement[] stackTrace = ex.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            StringBuilder stackInfo = new StringBuilder();
            // 只取前5行堆栈信息
            int maxLines = Math.min(5, stackTrace.length);
            for (int i = 0; i < maxLines; i++) {
                stackInfo.append(stackTrace[i].toString());
                if (i < maxLines - 1) {
                    stackInfo.append(" -> ");
                }
            }
            alertInfo.put("stackTrace", stackInfo.toString());
        }
        
        // 请求参数（敏感信息需要过滤）
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (parameterMap != null && !parameterMap.isEmpty()) {
            Map<String, Object> filteredParams = new HashMap<>();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue();
                
                // 过滤敏感参数
                if (isSensitiveParam(key)) {
                    filteredParams.put(key, "***");
                } else if (values != null && values.length > 0) {
                    filteredParams.put(key, values.length == 1 ? values[0] : values);
                }
            }
            alertInfo.put("requestParams", filteredParams);
        }
        
        return alertInfo;
    }
    
    /**
     * 获取客户端真实IP地址
     * 
     * @param request 请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 判断是否为敏感参数
     * 
     * @param paramName 参数名
     * @return true-敏感参数，false-非敏感参数
     */
    private boolean isSensitiveParam(String paramName) {
        if (paramName == null) {
            return false;
        }
        
        String lowerParamName = paramName.toLowerCase();
        return lowerParamName.contains("password") || 
               lowerParamName.contains("pwd") ||
               lowerParamName.contains("token") ||
               lowerParamName.contains("secret") ||
               lowerParamName.contains("key") ||
               lowerParamName.contains("idno") ||
               lowerParamName.contains("mobile") ||
               lowerParamName.contains("phone");
    }
}
